<template>
  <div class="main">
    <a-tabs class="filterTabs w-full" v-model:activeKey="activeTab" @change="search">
      <a-tab-pane v-for="item in tabList" :key="item.key">
        <template #tab>
          <a-badge :count="item.key === 20 ? redPoint : 0" :offset="[12, -2]" :overflowCount="100000">
            <span>{{ item.label }}</span>
          </a-badge>
        </template>
      </a-tab-pane>
    </a-tabs>
    <SearchForm ref="formRef" v-model:form="formArr" :page-type="PageType.SUPPLIER_SETTLEMENT_APPROVAL" @search="search" @setting="tableRef?.showTableSetting()"></SearchForm>
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.SUPPLIER_SETTLEMENT_APPROVAL" :get-list="GetUserList" :isCheckbox="true" :is-index="true">
      <template #operate="{ row }">
        <a-button @click="handleExamine(row.id, 'examine')" type="text" v-if="[20, 30].includes(row.audit_status) && btnPermission[42002]">审核</a-button>
        <a-button @click="handleExamine(row.id, 'view')" type="text" v-else>查看</a-button>
      </template>
      <template #audit_status_string="{ row }">
        <a-tooltip v-if="row.audit_status != 20">
          <!-- <template #title>
            <span>审核人：{{ row.auditor_name }}</span>
            <br />
            <span>审核时间：{{ row.audit_time }}</span>
          </template> -->
          <span>{{ row.audit_status_string }}</span>
        </a-tooltip>
        <span v-else>{{ row.audit_status_string }}</span>
      </template>
      <template #mainCategorie="{ row }">
        <span v-for="(item, index) in row.main_categories_strings" :key="item">{{ item }}{{ index !== row.main_categories_strings.length - 1 ? '，' : '' }}</span>
      </template>
    </BaseTable>
    <SupplierExamine ref="supplierExamineRef" @search="search" @getRedPoint="getRedPoint" />
  </div>
</template>

<script setup lang="ts">
import { PageType } from '@/common/enum'
import SearchForm from '@/components/SearchForm/index.vue'
import { GetSupplierSettlementApprovalList, GetEnumList, GetMainCategoryList } from '@/servers/supplierSettlementApproval'
import useBadgeStore from '@/store/modules/badgeStore'

import SupplierExamine from './components/SupplierExamine.vue'

const badgeStore = useBadgeStore()
const { btnPermission } = usePermission()

const tableRef = ref()
const formRef = ref()
const tabList = [
  { key: null, label: '全部' },
  { key: 20, label: '待审核' },
  { key: 90, label: '审核通过' },
  { key: 95, label: '审核不通过' },
]
const activeTab = ref<any>() // 默认全部

// 标签待审核数量
const redPoint = ref(0)
const search = () => tableRef.value.search()
const GetUserList = async (params: any) => {
  if (params.main_categories) {
    params.main_categories = [params.main_categories]
  }
  params.audit_status = activeTab.value
  params.list_type = 0
  params.sortField = 'application_at'
  params.sortType = 'desc'
  const res = await GetSupplierSettlementApprovalList(params)
  return {
    data: {
      list: res.data.list,
      total: res.data.total,
    },
  }
}
const supplierExamineRef = ref<any>(null)
const formArr = ref<any>([
  {
    label: '供应商编码',
    value: null,
    type: 'input',
    key: 'number',
  },
  {
    label: '供应商名称',
    value: null,
    type: 'input',
    key: 'supplier_name',
  },
  {
    label: '公司类型',
    value: null,
    type: 'select',
    key: 'company_type',
    options: [],
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['application_start_at', 'application_end_at'],
    placeholder: ['申请开始时间', '申请结束时间'],
  },
  {
    width: 200,
    label: '主营类目：商品一级类目',
    type: 'select',
    showTooltip: true,
    key: 'main_categories',
    options: [],
  },
])

const handleExamine = (id: number, type: string) => {
  supplierExamineRef.value.showDrawer(id, type)
}

// 获取下拉框
const getSelectList = async () => {
  const res = await GetEnumList()
  formArr.value[2].options = res.data.companytype_list.map((item: any) => ({
    label: item.label,
    value: Number(item.value),
  }))

  formArr.value[3].options = res.data.supplierauditstatus_list.map((item: any) => ({
    label: item.label,
    value: Number(item.value),
  }))
}
// 获取主营类目下拉框
const getMainCategoryList = async () => {
  const params = {
    page: 1,
    pageSize: 9999,
  }
  const res = await GetMainCategoryList(params)
  formArr.value[4].options = res.data.list.map((item: any) => ({
    label: item.content,
    value: item.id,
  }))
}
// 获取标签页的审核数量
const getRedPoint = async () => {
  badgeStore.fetchBadgeCounts().then(() => {
    redPoint.value = badgeStore.badgeCounts?.managementSupplierAuditLabelStatusCount?.auditing_count
  })
}
onMounted(async () => {
  getSelectList()
  getMainCategoryList()
  getRedPoint()
})
</script>

<style scoped lang="scss"></style>
